# AI模型相关配置
ai:
  baseUrl: "http://your-internal-llm-host:8000/v1"
  model: "your-enterprise-model-name"
  apiKey: "sk-your-internal-api-key"
  timeoutSeconds: 180

# RAG (Retrieval-Augmented Generation) 配置
rag:
  # 存放代码规范文档 (Markdown文件) 的目录路径
  knowledgeBasePath: "./knowledge_base"
  # Lucene索引文件存放的目录路径
  indexPath: "./.review_index"
  # 在检索时，返回最相关的前K个规范文档
  topK: 5

# Prompt模板配置
prompt:
  # 整体评审目标
  overallGoal: "你是一名资深的代码评审专家。你的任务是根据下面提供的【相关代码规范】和通用编程最佳实践，对给出的代码变更进行严格、细致的审查。你的反馈必须具有建设性，并提供清晰的修改建议。"
  # 输出格式要求
  outputFormat: |
    请严格按照以下Markdown格式输出评审报告：
    
    ### 总体评价
    [对本次提交的代码质量进行简短的总体评价，说明其是否遵循了提供的规范。]
    
    ### 详细评审建议
    
    **文件名: `[文件路径]`**
    
    *   **问题1**: [问题的简要描述]
        *   **行号**: [问题代码所在的行号]
        *   **相关规范**: [引用具体是哪条检索到的规范，或者通用编程原则]
        *   **问题代码**: 
          ```java
          [存在问题的代码片段]
          ```
        *   **分析与建议**: [详细解释为什么这是个问题，它如何违反了规范，并提供具体的、可操作的修改方案。]
    
    *   **问题2**: ...
    
    ---
    
    [如果一个文件没有问题，可以跳过。如果所有文件都没有问题，请明确指出"经过仔细审查，未发现明显问题，代码质量良好。"]
