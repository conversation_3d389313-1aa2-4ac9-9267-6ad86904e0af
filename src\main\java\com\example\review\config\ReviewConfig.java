package com.example.review.config;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 代码评审工具的配置类
 */
public class ReviewConfig {
    
    @JsonProperty("ai")
    private AiConfig ai;
    
    @JsonProperty("rag")
    private RagConfig rag;
    
    @JsonProperty("prompt")
    private PromptConfig prompt;
    
    // Getters and setters
    public AiConfig getAi() {
        return ai;
    }
    
    public void setAi(AiConfig ai) {
        this.ai = ai;
    }
    
    public RagConfig getRag() {
        return rag;
    }
    
    public void setRag(RagConfig rag) {
        this.rag = rag;
    }
    
    public PromptConfig getPrompt() {
        return prompt;
    }
    
    public void setPrompt(PromptConfig prompt) {
        this.prompt = prompt;
    }
    
    /**
     * AI模型配置
     */
    public static class AiConfig {
        @JsonProperty("baseUrl")
        private String baseUrl;
        
        @JsonProperty("model")
        private String model;
        
        @JsonProperty("apiKey")
        private String apiKey;
        
        @JsonProperty("timeoutSeconds")
        private int timeoutSeconds;
        
        // Getters and setters
        public String getBaseUrl() {
            return baseUrl;
        }
        
        public void setBaseUrl(String baseUrl) {
            this.baseUrl = baseUrl;
        }
        
        public String getModel() {
            return model;
        }
        
        public void setModel(String model) {
            this.model = model;
        }
        
        public String getApiKey() {
            return apiKey;
        }
        
        public void setApiKey(String apiKey) {
            this.apiKey = apiKey;
        }
        
        public int getTimeoutSeconds() {
            return timeoutSeconds;
        }
        
        public void setTimeoutSeconds(int timeoutSeconds) {
            this.timeoutSeconds = timeoutSeconds;
        }
    }
    
    /**
     * RAG配置
     */
    public static class RagConfig {
        @JsonProperty("knowledgeBasePath")
        private String knowledgeBasePath;
        
        @JsonProperty("indexPath")
        private String indexPath;
        
        @JsonProperty("topK")
        private int topK;
        
        // Getters and setters
        public String getKnowledgeBasePath() {
            return knowledgeBasePath;
        }
        
        public void setKnowledgeBasePath(String knowledgeBasePath) {
            this.knowledgeBasePath = knowledgeBasePath;
        }
        
        public String getIndexPath() {
            return indexPath;
        }
        
        public void setIndexPath(String indexPath) {
            this.indexPath = indexPath;
        }
        
        public int getTopK() {
            return topK;
        }
        
        public void setTopK(int topK) {
            this.topK = topK;
        }
    }
    
    /**
     * Prompt模板配置
     */
    public static class PromptConfig {
        @JsonProperty("overallGoal")
        private String overallGoal;
        
        @JsonProperty("outputFormat")
        private String outputFormat;
        
        // Getters and setters
        public String getOverallGoal() {
            return overallGoal;
        }
        
        public void setOverallGoal(String overallGoal) {
            this.overallGoal = overallGoal;
        }
        
        public String getOutputFormat() {
            return outputFormat;
        }
        
        public void setOutputFormat(String outputFormat) {
            this.outputFormat = outputFormat;
        }
    }
}
