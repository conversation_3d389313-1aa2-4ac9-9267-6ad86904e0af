# 规范：Java异常处理

## 资源管理
- **强制**: 必须使用 `try-with-resources` 或在 `finally` 块中可靠地关闭外部资源（如流、连接、会话）。
- **示例**: 
  ```java
  // 正确做法
  try (FileInputStream fis = new FileInputStream("file.txt")) {
      // 处理文件
  } catch (IOException e) {
      log.error("文件处理失败", e);
  }
  ```

## 异常捕获
- **强制**: 禁止生吞异常（即空的catch块）。如果确实不需要处理，必须在注释中说明原因。
- **禁止**: 捕获过于宽泛的异常，如 `catch (Exception e)` 或 `catch (Throwable t)`，除非它是应用顶层的最后一道防线。
- **示例**:
  ```java
  // 错误做法
  try {
      riskyOperation();
  } catch (Exception e) {
      // 空catch块，生吞异常
  }
  
  // 正确做法
  try {
      riskyOperation();
  } catch (SpecificException e) {
      log.error("操作失败: {}", e.getMessage(), e);
      throw new BusinessException("业务操作失败", e);
  }
  ```

## 日志记录
- **建议**: 在日志中记录异常时，务必包含完整的堆栈跟踪（stack trace）。
- **建议**: 异常信息应该包含足够的上下文信息，便于问题定位。

## 自定义异常
- **建议**: 创建有意义的自定义异常类，而不是抛出通用的RuntimeException。
- **建议**: 异常消息应该清晰描述问题和可能的解决方案。
