package com.example.review.ai;

import com.example.review.config.ReviewConfig;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.time.Duration;
import java.util.List;

/**
 * OpenAI兼容API服务，负责调用大语言模型进行代码评审
 */
public class OpenAIService {
    
    private static final Logger logger = LoggerFactory.getLogger(OpenAIService.class);
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");
    
    private final ReviewConfig.AiConfig aiConfig;
    private final ReviewConfig.PromptConfig promptConfig;
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;
    
    public OpenAIService(ReviewConfig.AiConfig aiConfig, ReviewConfig.PromptConfig promptConfig) {
        this.aiConfig = aiConfig;
        this.promptConfig = promptConfig;
        this.objectMapper = new ObjectMapper();
        
        // 配置HTTP客户端
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(Duration.ofSeconds(30))
                .readTimeout(Duration.ofSeconds(aiConfig.getTimeoutSeconds()))
                .writeTimeout(Duration.ofSeconds(30))
                .build();
    }
    
    /**
     * 执行代码评审
     * 
     * @param codeDiff 代码差异
     * @param relevantRules 相关规范列表
     * @return AI评审结果
     * @throws IOException HTTP请求失败
     */
    public String performReview(String codeDiff, List<String> relevantRules) throws IOException {
        logger.info("Starting AI code review...");
        
        // 构建完整的prompt
        String fullPrompt = buildPrompt(codeDiff, relevantRules);
        
        logger.debug("Prompt length: {} characters", fullPrompt.length());
        
        // 构建请求体
        ObjectNode requestBody = buildRequestBody(fullPrompt);
        
        // 发送HTTP请求
        String requestJson = objectMapper.writeValueAsString(requestBody);
        RequestBody body = RequestBody.create(requestJson, JSON);
        
        Request request = new Request.Builder()
                .url(aiConfig.getBaseUrl() + "/chat/completions")
                .addHeader("Authorization", "Bearer " + aiConfig.getApiKey())
                .addHeader("Content-Type", "application/json")
                .post(body)
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "No error details";
                throw new IOException("AI API request failed: " + response.code() + " " + response.message() + 
                        "\nError details: " + errorBody);
            }
            
            String responseBody = response.body().string();
            return parseResponse(responseBody);
        }
    }
    
    /**
     * 构建完整的prompt
     * 
     * @param codeDiff 代码差异
     * @param relevantRules 相关规范列表
     * @return 完整的prompt
     */
    private String buildPrompt(String codeDiff, List<String> relevantRules) {
        StringBuilder prompt = new StringBuilder();
        
        // 添加总体目标
        prompt.append(promptConfig.getOverallGoal()).append("\n\n");
        
        // 添加相关规范
        if (!relevantRules.isEmpty()) {
            prompt.append("【相关代码规范】\n");
            for (int i = 0; i < relevantRules.size(); i++) {
                prompt.append("规范 ").append(i + 1).append(":\n");
                prompt.append(relevantRules.get(i)).append("\n\n");
            }
        } else {
            prompt.append("【相关代码规范】\n");
            prompt.append("未检索到特定规范，请基于通用编程最佳实践进行评审。\n\n");
        }
        
        // 添加输出格式要求
        prompt.append("【输出格式要求】\n");
        prompt.append(promptConfig.getOutputFormat()).append("\n\n");
        
        // 添加代码差异
        prompt.append("【待评审的代码变更】\n");
        prompt.append("```diff\n");
        prompt.append(codeDiff);
        prompt.append("\n```\n\n");
        
        prompt.append("请开始评审：");
        
        return prompt.toString();
    }
    
    /**
     * 构建OpenAI API请求体
     * 
     * @param prompt 完整的prompt
     * @return 请求体JSON对象
     */
    private ObjectNode buildRequestBody(String prompt) {
        ObjectNode requestBody = objectMapper.createObjectNode();
        
        // 设置模型
        requestBody.put("model", aiConfig.getModel());
        
        // 设置消息
        ArrayNode messages = objectMapper.createArrayNode();
        ObjectNode systemMessage = objectMapper.createObjectNode();
        systemMessage.put("role", "user");
        systemMessage.put("content", prompt);
        messages.add(systemMessage);
        requestBody.set("messages", messages);
        
        // 设置其他参数
        requestBody.put("temperature", 0.1); // 较低的温度以获得更一致的输出
        requestBody.put("max_tokens", 4000); // 限制输出长度
        requestBody.put("top_p", 0.9);
        requestBody.put("frequency_penalty", 0.0);
        requestBody.put("presence_penalty", 0.0);
        
        return requestBody;
    }
    
    /**
     * 解析AI API响应
     * 
     * @param responseBody 响应体JSON字符串
     * @return AI生成的评审内容
     * @throws IOException JSON解析失败
     */
    private String parseResponse(String responseBody) throws IOException {
        try {
            JsonNode responseJson = objectMapper.readTree(responseBody);
            
            // 检查是否有错误
            if (responseJson.has("error")) {
                JsonNode error = responseJson.get("error");
                String errorMessage = error.has("message") ? error.get("message").asText() : "Unknown error";
                throw new IOException("AI API returned error: " + errorMessage);
            }
            
            // 提取生成的内容
            JsonNode choices = responseJson.get("choices");
            if (choices == null || !choices.isArray() || choices.size() == 0) {
                throw new IOException("Invalid response format: no choices found");
            }
            
            JsonNode firstChoice = choices.get(0);
            JsonNode message = firstChoice.get("message");
            if (message == null) {
                throw new IOException("Invalid response format: no message found");
            }
            
            String content = message.get("content").asText();
            
            // 记录使用情况（如果有的话）
            if (responseJson.has("usage")) {
                JsonNode usage = responseJson.get("usage");
                int promptTokens = usage.has("prompt_tokens") ? usage.get("prompt_tokens").asInt() : 0;
                int completionTokens = usage.has("completion_tokens") ? usage.get("completion_tokens").asInt() : 0;
                int totalTokens = usage.has("total_tokens") ? usage.get("total_tokens").asInt() : 0;
                
                logger.info("Token usage - Prompt: {}, Completion: {}, Total: {}", 
                        promptTokens, completionTokens, totalTokens);
            }
            
            logger.info("AI code review completed successfully");
            return content;
            
        } catch (Exception e) {
            logger.error("Failed to parse AI response: {}", responseBody, e);
            throw new IOException("Failed to parse AI response: " + e.getMessage(), e);
        }
    }
    
    /**
     * 关闭HTTP客户端
     */
    public void close() {
        if (httpClient != null) {
            httpClient.dispatcher().executorService().shutdown();
            httpClient.connectionPool().evictAll();
        }
    }
}
