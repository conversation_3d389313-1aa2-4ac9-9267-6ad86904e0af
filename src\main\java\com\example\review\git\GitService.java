package com.example.review.git;

import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.api.errors.GitAPIException;
import org.eclipse.jgit.diff.DiffEntry;
import org.eclipse.jgit.diff.DiffFormatter;
import org.eclipse.jgit.lib.ObjectId;
import org.eclipse.jgit.lib.ObjectReader;
import org.eclipse.jgit.lib.Repository;
import org.eclipse.jgit.revwalk.RevCommit;
import org.eclipse.jgit.revwalk.RevTree;
import org.eclipse.jgit.revwalk.RevWalk;
import org.eclipse.jgit.storage.file.FileRepositoryBuilder;
import org.eclipse.jgit.treewalk.AbstractTreeIterator;
import org.eclipse.jgit.treewalk.CanonicalTreeParser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * Git服务，负责获取代码差异
 */
public class GitService {
    
    private static final Logger logger = LoggerFactory.getLogger(GitService.class);
    
    /**
     * 获取两个commit之间的Java文件差异
     * 
     * @param repoPath Git仓库路径
     * @param oldCommit 旧commit的哈希值
     * @param newCommit 新commit的哈希值
     * @return 格式化的diff文本
     * @throws IOException Git操作失败
     * @throws GitAPIException Git API调用失败
     */
    public String getDiff(String repoPath, String oldCommit, String newCommit) throws IOException, GitAPIException {
        logger.info("Getting diff between {} and {} in repository: {}", oldCommit, newCommit, repoPath);
        
        File repoDir = new File(repoPath);
        if (!repoDir.exists() || !repoDir.isDirectory()) {
            throw new IOException("Repository directory does not exist: " + repoPath);
        }
        
        // 查找.git目录
        File gitDir = findGitDirectory(repoDir);
        if (gitDir == null) {
            throw new IOException("Not a git repository: " + repoPath);
        }
        
        try (Repository repository = new FileRepositoryBuilder()
                .setGitDir(gitDir)
                .readEnvironment()
                .findGitDir()
                .build()) {
            
            try (Git git = new Git(repository)) {
                // 解析commit对象
                ObjectId oldCommitId = repository.resolve(oldCommit);
                ObjectId newCommitId = repository.resolve(newCommit);
                
                if (oldCommitId == null) {
                    throw new IllegalArgumentException("Cannot resolve old commit: " + oldCommit);
                }
                if (newCommitId == null) {
                    throw new IllegalArgumentException("Cannot resolve new commit: " + newCommit);
                }
                
                // 获取commit的tree
                AbstractTreeIterator oldTreeParser = prepareTreeParser(repository, oldCommitId);
                AbstractTreeIterator newTreeParser = prepareTreeParser(repository, newCommitId);
                
                // 获取diff条目
                List<DiffEntry> diffs = git.diff()
                        .setOldTree(oldTreeParser)
                        .setNewTree(newTreeParser)
                        .call();
                
                // 过滤只包含Java文件的diff
                List<DiffEntry> javaDiffs = diffs.stream()
                        .filter(this::isJavaFile)
                        .toList();
                
                logger.info("Found {} total diffs, {} Java file diffs", diffs.size(), javaDiffs.size());
                
                if (javaDiffs.isEmpty()) {
                    return "No Java file changes found between the specified commits.";
                }
                
                // 格式化diff输出
                return formatDiffs(repository, javaDiffs);
            }
        }
    }
    
    /**
     * 查找Git目录
     * 
     * @param startDir 开始搜索的目录
     * @return Git目录，如果未找到返回null
     */
    private File findGitDirectory(File startDir) {
        File current = startDir;
        while (current != null) {
            File gitDir = new File(current, ".git");
            if (gitDir.exists()) {
                if (gitDir.isDirectory()) {
                    return gitDir;
                } else if (gitDir.isFile()) {
                    // 处理git worktree的情况
                    // 这里简化处理，实际项目中可能需要解析.git文件内容
                    return gitDir.getParentFile();
                }
            }
            current = current.getParentFile();
        }
        return null;
    }
    
    /**
     * 准备树解析器
     * 
     * @param repository Git仓库
     * @param commitId commit ID
     * @return 树解析器
     * @throws IOException 操作失败
     */
    private AbstractTreeIterator prepareTreeParser(Repository repository, ObjectId commitId) throws IOException {
        try (RevWalk walk = new RevWalk(repository)) {
            RevCommit commit = walk.parseCommit(commitId);
            RevTree tree = walk.parseTree(commit.getTree().getId());
            
            CanonicalTreeParser treeParser = new CanonicalTreeParser();
            try (ObjectReader reader = repository.newObjectReader()) {
                treeParser.reset(reader, tree.getId());
            }
            
            walk.dispose();
            return treeParser;
        }
    }
    
    /**
     * 判断是否为Java文件
     * 
     * @param diffEntry diff条目
     * @return 是否为Java文件
     */
    private boolean isJavaFile(DiffEntry diffEntry) {
        String oldPath = diffEntry.getOldPath();
        String newPath = diffEntry.getNewPath();
        
        return (oldPath != null && oldPath.endsWith(".java")) ||
               (newPath != null && newPath.endsWith(".java"));
    }
    
    /**
     * 格式化diff输出
     * 
     * @param repository Git仓库
     * @param diffs diff条目列表
     * @return 格式化的diff文本
     * @throws IOException 操作失败
     */
    private String formatDiffs(Repository repository, List<DiffEntry> diffs) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        
        try (DiffFormatter formatter = new DiffFormatter(outputStream)) {
            formatter.setRepository(repository);
            formatter.setContext(3); // 设置上下文行数
            
            for (DiffEntry diff : diffs) {
                formatter.format(diff);
            }
        }
        
        String diffText = outputStream.toString();
        
        // 添加一些元信息
        StringBuilder result = new StringBuilder();
        result.append("=== Java File Changes Summary ===\n");
        result.append("Total files changed: ").append(diffs.size()).append("\n");
        
        for (DiffEntry diff : diffs) {
            result.append("- ").append(diff.getChangeType()).append(": ");
            switch (diff.getChangeType()) {
                case ADD -> result.append(diff.getNewPath());
                case DELETE -> result.append(diff.getOldPath());
                case MODIFY, RENAME, COPY -> result.append(diff.getOldPath()).append(" -> ").append(diff.getNewPath());
            }
            result.append("\n");
        }
        
        result.append("\n=== Detailed Changes ===\n");
        result.append(diffText);
        
        return result.toString();
    }
}
