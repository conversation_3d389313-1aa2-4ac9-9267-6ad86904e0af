package com.example.review.cli;

import com.example.review.config.ConfigLoader;
import com.example.review.config.ReviewConfig;
import com.example.review.rag.IndexingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import picocli.CommandLine.Command;
import picocli.CommandLine.Option;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.concurrent.Callable;

/**
 * 索引命令，负责创建和更新知识库索引
 */
@Command(
    name = "index",
    description = "Create or update the knowledge base index for code review rules",
    mixinStandardHelpOptions = true
)
public class IndexCommand implements Callable<Integer> {
    
    private static final Logger logger = LoggerFactory.getLogger(IndexCommand.class);
    
    @Option(
        names = {"-c", "--config"},
        description = "Configuration file path (default: config.yml)",
        defaultValue = "config.yml"
    )
    private String configPath;
    
    @Option(
        names = {"-f", "--force"},
        description = "Force rebuild index even if it already exists"
    )
    private boolean force;
    
    @Option(
        names = {"-v", "--verbose"},
        description = "Enable verbose output"
    )
    private boolean verbose;
    
    @Override
    public Integer call() throws Exception {
        try {
            // 设置日志级别
            if (verbose) {
                System.setProperty("org.slf4j.simpleLogger.defaultLogLevel", "DEBUG");
            }
            
            logger.info("Starting knowledge base indexing...");
            
            // 加载配置
            ConfigLoader configLoader = new ConfigLoader();
            ReviewConfig config = configLoader.loadConfig(configPath);
            
            // 验证知识库目录
            String knowledgeBasePath = config.getRag().getKnowledgeBasePath();
            if (!Files.exists(Paths.get(knowledgeBasePath))) {
                System.err.println("Error: Knowledge base directory does not exist: " + knowledgeBasePath);
                System.err.println("Please create the directory and add some .md files with coding standards.");
                return 1;
            }
            
            // 检查是否需要强制重建
            String indexPath = config.getRag().getIndexPath();
            if (!force && Files.exists(Paths.get(indexPath))) {
                System.out.println("Index already exists at: " + indexPath);
                System.out.println("Use --force to rebuild the index.");
                return 0;
            }
            
            // 创建索引服务并执行索引
            try (IndexingService indexingService = new IndexingService(config.getRag())) {
                long startTime = System.currentTimeMillis();
                
                indexingService.createOrUpdateIndex();
                
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                
                System.out.println("✓ Knowledge base indexed successfully!");
                System.out.println("  Knowledge base: " + knowledgeBasePath);
                System.out.println("  Index location: " + indexPath);
                System.out.println("  Time taken: " + duration + "ms");
                
                logger.info("Indexing completed in {}ms", duration);
                return 0;
            }
            
        } catch (IOException e) {
            logger.error("IO error during indexing", e);
            System.err.println("Error: " + e.getMessage());
            return 1;
        } catch (Exception e) {
            logger.error("Unexpected error during indexing", e);
            System.err.println("Unexpected error: " + e.getMessage());
            if (verbose) {
                e.printStackTrace();
            }
            return 1;
        }
    }
}
