package com.example.review.rag;

import com.example.review.config.ReviewConfig;
import org.apache.lucene.analysis.standard.StandardAnalyzer;
import org.apache.lucene.document.Document;
import org.apache.lucene.index.DirectoryReader;
import org.apache.lucene.index.IndexReader;
import org.apache.lucene.queryparser.classic.MultiFieldQueryParser;
import org.apache.lucene.queryparser.classic.ParseException;
import org.apache.lucene.queryparser.classic.QueryParser;
import org.apache.lucene.search.IndexSearcher;
import org.apache.lucene.search.Query;
import org.apache.lucene.search.ScoreDoc;
import org.apache.lucene.search.TopDocs;
import org.apache.lucene.store.Directory;
import org.apache.lucene.store.FSDirectory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * Lucene检索服务，负责从索引中检索相关的代码规范文档
 */
public class RetrievalService implements AutoCloseable {
    
    private static final Logger logger = LoggerFactory.getLogger(RetrievalService.class);
    
    private final ReviewConfig.RagConfig ragConfig;
    private final StandardAnalyzer analyzer;
    
    public RetrievalService(ReviewConfig.RagConfig ragConfig) {
        this.ragConfig = ragConfig;
        this.analyzer = new StandardAnalyzer();
    }
    
    /**
     * 检索与查询文本相关的规范文档
     * 
     * @param queryText 查询文本（通常是代码diff）
     * @return 相关规范文档内容列表
     * @throws IOException 索引读取失败
     * @throws ParseException 查询解析失败
     */
    public List<String> retrieveRelevantRules(String queryText) throws IOException, ParseException {
        Path indexPath = Paths.get(ragConfig.getIndexPath());
        
        // 验证索引目录存在
        if (!Files.exists(indexPath) || !Files.isDirectory(indexPath)) {
            throw new IOException("Index directory does not exist: " + indexPath + 
                    ". Please run 'index' command first to create the index.");
        }
        
        List<String> relevantRules = new ArrayList<>();
        
        try (Directory directory = FSDirectory.open(indexPath);
             IndexReader reader = DirectoryReader.open(directory)) {
            
            IndexSearcher searcher = new IndexSearcher(reader);
            
            // 创建多字段查询解析器，搜索标题和内容字段
            String[] fields = {IndexingService.FIELD_TITLE, IndexingService.FIELD_CONTENT};
            MultiFieldQueryParser parser = new MultiFieldQueryParser(fields, analyzer);
            
            // 预处理查询文本
            String processedQuery = preprocessQuery(queryText);
            
            logger.debug("Searching with query: {}", processedQuery);
            
            // 解析查询
            Query query = parser.parse(processedQuery);
            
            // 执行搜索
            TopDocs topDocs = searcher.search(query, ragConfig.getTopK());
            
            logger.info("Found {} relevant documents (requested top {})", 
                    topDocs.scoreDocs.length, ragConfig.getTopK());
            
            // 提取搜索结果
            for (ScoreDoc scoreDoc : topDocs.scoreDocs) {
                Document doc = searcher.doc(scoreDoc.doc);
                String content = doc.get(IndexingService.FIELD_CONTENT);
                String path = doc.get(IndexingService.FIELD_PATH);
                String title = doc.get(IndexingService.FIELD_TITLE);
                
                logger.debug("Retrieved document: {} (score: {}, title: {})", 
                        path, scoreDoc.score, title);
                
                relevantRules.add(content);
            }
        }
        
        return relevantRules;
    }
    
    /**
     * 预处理查询文本，提取关键词并构建Lucene查询
     * 
     * @param queryText 原始查询文本
     * @return 处理后的查询字符串
     */
    private String preprocessQuery(String queryText) {
        if (queryText == null || queryText.trim().isEmpty()) {
            return "*:*"; // 如果查询为空，返回所有文档
        }
        
        // 移除特殊字符，保留字母、数字、空格和一些常见符号
        String cleaned = queryText.replaceAll("[^\\w\\s+\\-_.]", " ");
        
        // 提取Java相关关键词
        List<String> keywords = extractJavaKeywords(cleaned);
        
        // 如果没有提取到关键词，使用原始文本的清理版本
        if (keywords.isEmpty()) {
            // 分词并转义特殊字符
            String[] words = cleaned.trim().split("\\s+");
            List<String> validWords = new ArrayList<>();
            for (String word : words) {
                if (word.length() > 2) { // 过滤太短的词
                    validWords.add(QueryParser.escape(word));
                }
            }
            return validWords.isEmpty() ? "*:*" : String.join(" OR ", validWords);
        }
        
        // 构建查询字符串
        return String.join(" OR ", keywords);
    }
    
    /**
     * 从文本中提取Java相关关键词
     * 
     * @param text 输入文本
     * @return 关键词列表
     */
    private List<String> extractJavaKeywords(String text) {
        List<String> keywords = new ArrayList<>();
        String lowerText = text.toLowerCase();
        
        // Java关键词映射
        String[][] keywordMappings = {
                {"exception", "异常", "throw", "catch", "try", "finally"},
                {"class", "interface", "abstract", "类", "接口"},
                {"method", "function", "方法", "函数"},
                {"variable", "field", "变量", "字段"},
                {"string", "字符串", "concat", "append"},
                {"loop", "for", "while", "循环"},
                {"performance", "性能", "optimize", "优化"},
                {"naming", "命名", "convention", "规范"},
                {"null", "空指针", "npe"},
                {"collection", "list", "map", "set", "集合"},
                {"stream", "lambda", "流"},
                {"synchronized", "thread", "并发", "线程"},
                {"annotation", "注解"},
                {"generics", "泛型"},
                {"inheritance", "继承", "extends", "implements"}
        };
        
        // 检查每个关键词组
        for (String[] group : keywordMappings) {
            for (String keyword : group) {
                if (lowerText.contains(keyword)) {
                    keywords.add(QueryParser.escape(group[0])); // 使用组的第一个词作为标准关键词
                    break; // 找到一个就跳出当前组
                }
            }
        }
        
        return keywords;
    }
    
    /**
     * 关闭资源
     */
    @Override
    public void close() throws IOException {
        if (analyzer != null) {
            analyzer.close();
        }
    }
}
