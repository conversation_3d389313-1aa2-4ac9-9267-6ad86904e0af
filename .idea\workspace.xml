<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectId" id="2y8exFKcJ6WvGJXaObVZ5BTXl3L" />
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "dart.analysis.tool.window.visible": "false",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "E:/CODE/project/AICodeViewer",
    "show.migrate.to.gradle.popup": "false"
  }
}]]></component>
  <component name="RunManager">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="AICodeViewer" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="AICodeViewer" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
  </component>
</project>