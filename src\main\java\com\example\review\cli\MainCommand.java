package com.example.review.cli;

import picocli.CommandLine;
import picocli.CommandLine.Command;

/**
 * 主命令类，定义应用程序的入口点和子命令
 */
@Command(
    name = "ai-reviewer",
    description = "AI-powered code review assistant using RAG architecture",
    version = "1.0.0",
    mixinStandardHelpOptions = true,
    subcommands = {
        IndexCommand.class,
        ReviewCommand.class
    }
)
public class MainCommand implements Runnable {
    
    @Override
    public void run() {
        // 如果没有指定子命令，显示帮助信息
        System.out.println("AI Code Review Assistant v1.0.0");
        System.out.println();
        System.out.println("Available commands:");
        System.out.println("  index   - Create or update the knowledge base index");
        System.out.println("  review  - Perform AI-powered code review");
        System.out.println();
        System.out.println("Use 'ai-reviewer <command> --help' for more information about a command.");
    }
    
    /**
     * 应用程序入口点
     * 
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        // 配置日志级别（可以通过系统属性覆盖）
        if (System.getProperty("org.slf4j.simpleLogger.defaultLogLevel") == null) {
            System.setProperty("org.slf4j.simpleLogger.defaultLogLevel", "INFO");
        }
        
        // 创建命令行解析器
        CommandLine commandLine = new CommandLine(new MainCommand());
        
        // 设置错误处理
        commandLine.setExecutionExceptionHandler((ex, cmd, parseResult) -> {
            System.err.println("Error: " + ex.getMessage());
            if (Boolean.parseBoolean(System.getProperty("debug", "false"))) {
                ex.printStackTrace();
            }
            return 1;
        });
        
        // 执行命令
        int exitCode = commandLine.execute(args);
        System.exit(exitCode);
    }
}
