package com.example.review.cli;

import com.example.review.config.ConfigLoader;
import com.example.review.config.ReviewConfig;
import com.example.review.engine.ReviewEngine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import picocli.CommandLine.Command;
import picocli.CommandLine.Option;
import picocli.CommandLine.Parameters;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.concurrent.Callable;

/**
 * 评审命令，负责执行AI代码评审
 */
@Command(
    name = "review",
    description = "Perform AI-powered code review between two Git commits",
    mixinStandardHelpOptions = true
)
public class ReviewCommand implements Callable<Integer> {
    
    private static final Logger logger = LoggerFactory.getLogger(ReviewCommand.class);
    
    @Option(
        names = {"-c", "--config"},
        description = "Configuration file path (default: config.yml)",
        defaultValue = "config.yml"
    )
    private String configPath;
    
    @Option(
        names = {"-r", "--repo-path"},
        description = "Git repository path (default: current directory)",
        defaultValue = "."
    )
    private String repoPath;
    
    @Parameters(
        index = "0",
        description = "Old commit hash (e.g., feature-branch-start-hash)"
    )
    private String oldCommit;
    
    @Parameters(
        index = "1", 
        description = "New commit hash (e.g., HEAD)"
    )
    private String newCommit;
    
    @Option(
        names = {"-v", "--verbose"},
        description = "Enable verbose output"
    )
    private boolean verbose;
    
    @Option(
        names = {"--stats"},
        description = "Show configuration statistics before review"
    )
    private boolean showStats;
    
    @Override
    public Integer call() throws Exception {
        try {
            // 设置日志级别
            if (verbose) {
                System.setProperty("org.slf4j.simpleLogger.defaultLogLevel", "DEBUG");
            }
            
            logger.info("Starting AI code review...");
            
            // 验证参数
            if (!validateParameters()) {
                return 1;
            }
            
            // 加载配置
            ConfigLoader configLoader = new ConfigLoader();
            ReviewConfig config = configLoader.loadConfig(configPath);
            
            // 验证索引存在
            String indexPath = config.getRag().getIndexPath();
            if (!Files.exists(Paths.get(indexPath))) {
                System.err.println("Error: Knowledge base index not found at: " + indexPath);
                System.err.println("Please run 'ai-reviewer index' first to create the index.");
                return 1;
            }
            
            // 创建评审引擎
            try (ReviewEngine reviewEngine = new ReviewEngine(config)) {
                
                // 显示配置统计（如果需要）
                if (showStats) {
                    System.out.println(reviewEngine.getReviewStats());
                    System.out.println();
                }
                
                // 验证评审参数
                reviewEngine.validateReviewParameters(repoPath, oldCommit, newCommit);
                
                // 执行评审
                long startTime = System.currentTimeMillis();
                
                String reviewResult = reviewEngine.performReview(repoPath, oldCommit, newCommit);
                
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                
                // 输出评审结果
                System.out.println("=".repeat(80));
                System.out.println("AI CODE REVIEW REPORT");
                System.out.println("=".repeat(80));
                System.out.println("Repository: " + new File(repoPath).getAbsolutePath());
                System.out.println("Commits: " + oldCommit + " -> " + newCommit);
                System.out.println("Review time: " + duration + "ms");
                System.out.println("=".repeat(80));
                System.out.println();
                System.out.println(reviewResult);
                System.out.println();
                System.out.println("=".repeat(80));
                System.out.println("Review completed successfully!");
                
                logger.info("Code review completed in {}ms", duration);
                return 0;
            }
            
        } catch (ReviewEngine.ReviewException e) {
            logger.error("Review error", e);
            System.err.println("Review error: " + e.getMessage());
            return 1;
        } catch (Exception e) {
            logger.error("Unexpected error during review", e);
            System.err.println("Unexpected error: " + e.getMessage());
            if (verbose) {
                e.printStackTrace();
            }
            return 1;
        }
    }
    
    /**
     * 验证命令行参数
     * 
     * @return 验证是否通过
     */
    private boolean validateParameters() {
        // 验证仓库路径
        File repoDir = new File(repoPath);
        if (!repoDir.exists() || !repoDir.isDirectory()) {
            System.err.println("Error: Repository path does not exist or is not a directory: " + repoPath);
            return false;
        }
        
        // 验证commit哈希格式（简单验证）
        if (!isValidCommitHash(oldCommit)) {
            System.err.println("Error: Invalid old commit hash format: " + oldCommit);
            return false;
        }
        
        if (!isValidCommitHash(newCommit)) {
            System.err.println("Error: Invalid new commit hash format: " + newCommit);
            return false;
        }
        
        if (oldCommit.equals(newCommit)) {
            System.err.println("Error: Old commit and new commit cannot be the same");
            return false;
        }
        
        return true;
    }
    
    /**
     * 简单验证commit哈希格式
     * 
     * @param commitHash commit哈希
     * @return 是否有效
     */
    private boolean isValidCommitHash(String commitHash) {
        if (commitHash == null || commitHash.trim().isEmpty()) {
            return false;
        }
        
        // 允许HEAD等特殊引用
        if ("HEAD".equalsIgnoreCase(commitHash) || commitHash.startsWith("refs/")) {
            return true;
        }
        
        // 验证哈希格式（至少4个字符，只包含十六进制字符）
        return commitHash.matches("^[a-fA-F0-9]{4,}$");
    }
}
