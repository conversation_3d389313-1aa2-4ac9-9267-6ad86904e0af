# 规范：阿里巴巴Java命名风格

## 类命名
- **强制**: 类名必须使用大驼峰命名法（UpperCamelCase），如 `CodeReviewAssistant`。
- **强制**: 抽象类命名使用Abstract或Base开头；异常类命名使用Exception结尾；测试类命名以它要测试的类的名称开始，以Test结尾。

## 方法和变量命名
- **强制**: 方法名、参数名、成员变量、局部变量必须使用小驼峰命名法（lowerCamelCase），如 `performReview`。
- **强制**: 方法名必须是动词或动词短语，如 `saveUser`、`getUserById`。

## 常量命名
- **强制**: 常量名所有字母大写，单词间用下划线分隔，如 `MAX_RETRIES`、`DEFAULT_TIMEOUT`。
- **强制**: 常量必须使用 `public static final` 修饰。

## 包命名
- **强制**: 包名统一使用小写，点分隔符之间有且仅有一个自然语义的英语单词。包名统一使用单数形式，但是类名如果有复数含义，类名可以使用复数形式。
- **示例**: `com.example.review.service`（正确）vs `com.example.reviews.services`（错误）

## 禁止事项
- **禁止**: 使用拼音、中文、或不规范的缩写（如`a`、`b`、`c`）作为命名。命名应自解释。
- **禁止**: 使用下划线或美元符号开始和结束命名。
- **禁止**: 使用Java关键字和保留字作为命名。

## 布尔类型
- **强制**: POJO类中布尔类型变量都不要加is前缀，否则部分框架解析会引起序列化错误。
- **示例**: `boolean success;`（正确）vs `boolean isSuccess;`（错误）
