package com.example.review.rag;

import com.example.review.config.ReviewConfig;
import org.apache.lucene.analysis.standard.StandardAnalyzer;
import org.apache.lucene.document.Document;
import org.apache.lucene.document.Field;
import org.apache.lucene.document.StringField;
import org.apache.lucene.document.TextField;
import org.apache.lucene.index.IndexWriter;
import org.apache.lucene.index.IndexWriterConfig;
import org.apache.lucene.store.Directory;
import org.apache.lucene.store.FSDirectory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Stream;

/**
 * Lucene索引服务，负责为知识库文档创建和更新索引
 */
public class IndexingService implements AutoCloseable {
    
    private static final Logger logger = LoggerFactory.getLogger(IndexingService.class);
    
    // Lucene文档字段名
    public static final String FIELD_PATH = "path";
    public static final String FIELD_CONTENT = "content";
    public static final String FIELD_TITLE = "title";
    
    private final ReviewConfig.RagConfig ragConfig;
    private final StandardAnalyzer analyzer;
    
    public IndexingService(ReviewConfig.RagConfig ragConfig) {
        this.ragConfig = ragConfig;
        this.analyzer = new StandardAnalyzer();
    }
    
    /**
     * 创建或更新知识库索引
     * 
     * @throws IOException 索引创建失败
     */
    public void createOrUpdateIndex() throws IOException {
        Path knowledgeBasePath = Paths.get(ragConfig.getKnowledgeBasePath());
        Path indexPath = Paths.get(ragConfig.getIndexPath());
        
        // 验证知识库目录存在
        if (!Files.exists(knowledgeBasePath) || !Files.isDirectory(knowledgeBasePath)) {
            throw new IOException("Knowledge base directory does not exist: " + knowledgeBasePath);
        }
        
        // 创建索引目录
        Files.createDirectories(indexPath);
        
        logger.info("Starting indexing process...");
        logger.info("Knowledge base path: {}", knowledgeBasePath.toAbsolutePath());
        logger.info("Index path: {}", indexPath.toAbsolutePath());
        
        try (Directory directory = FSDirectory.open(indexPath)) {
            IndexWriterConfig config = new IndexWriterConfig(analyzer);
            config.setOpenMode(IndexWriterConfig.OpenMode.CREATE); // 总是重新创建索引
            
            try (IndexWriter writer = new IndexWriter(directory, config)) {
                // 递归遍历知识库目录，索引所有.md文件
                indexMarkdownFiles(writer, knowledgeBasePath);
                
                // 提交索引
                writer.commit();
                
                logger.info("Indexing completed successfully. Total documents: {}", writer.getDocStats().numDocs);
            }
        }
    }
    
    /**
     * 递归索引Markdown文件
     * 
     * @param writer IndexWriter
     * @param directory 目录路径
     * @throws IOException 文件读取失败
     */
    private void indexMarkdownFiles(IndexWriter writer, Path directory) throws IOException {
        try (Stream<Path> paths = Files.walk(directory)) {
            List<Path> markdownFiles = paths
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().toLowerCase().endsWith(".md"))
                    .toList();
            
            logger.info("Found {} markdown files to index", markdownFiles.size());
            
            for (Path filePath : markdownFiles) {
                indexSingleFile(writer, filePath);
            }
        }
    }
    
    /**
     * 索引单个文件
     * 
     * @param writer IndexWriter
     * @param filePath 文件路径
     * @throws IOException 文件读取失败
     */
    private void indexSingleFile(IndexWriter writer, Path filePath) throws IOException {
        try {
            String content = Files.readString(filePath);
            String relativePath = Paths.get(ragConfig.getKnowledgeBasePath()).relativize(filePath).toString();
            String title = extractTitle(content, filePath.getFileName().toString());
            
            Document document = new Document();
            
            // 添加字段
            document.add(new StringField(FIELD_PATH, relativePath, Field.Store.YES));
            document.add(new TextField(FIELD_CONTENT, content, Field.Store.YES));
            document.add(new TextField(FIELD_TITLE, title, Field.Store.YES));
            
            writer.addDocument(document);
            
            logger.debug("Indexed file: {} (title: {})", relativePath, title);
            
        } catch (IOException e) {
            logger.error("Failed to index file: {}", filePath, e);
            throw e;
        }
    }
    
    /**
     * 从Markdown内容中提取标题
     * 
     * @param content 文件内容
     * @param fileName 文件名（作为fallback）
     * @return 标题
     */
    private String extractTitle(String content, String fileName) {
        // 查找第一个# 标题
        String[] lines = content.split("\n");
        for (String line : lines) {
            line = line.trim();
            if (line.startsWith("# ")) {
                return line.substring(2).trim();
            }
        }
        
        // 如果没有找到标题，使用文件名（去掉扩展名）
        return fileName.replaceAll("\\.md$", "");
    }
    
    /**
     * 关闭资源
     */
    @Override
    public void close() throws IOException {
        if (analyzer != null) {
            analyzer.close();
        }
    }
}
