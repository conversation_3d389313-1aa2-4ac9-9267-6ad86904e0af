# AI Code Review Assistant

一个基于RAG（检索增强生成）架构的专业级Java命令行代码评审工具，使用Apache Lucene本地搜索引擎和大语言模型进行智能代码评审。

## 功能特性

- 🔍 **智能检索**: 使用Apache Lucene为代码规范创建本地搜索索引
- 🤖 **AI评审**: 集成大语言模型进行上下文相关的代码评审
- 📋 **规范管理**: 支持Markdown格式的代码规范知识库
- 🔄 **Git集成**: 自动提取两个commit之间的Java代码差异
- 📊 **详细报告**: 生成结构化的Markdown评审报告
- 🛠️ **命令行工具**: 支持子命令的专业命令行界面

## 系统要求

- Java 17+
- Maven 3.6+
- Git仓库（用于代码差异提取）

## 快速开始

### 1. 构建项目

```bash
mvn clean package
```

### 2. 配置文件

复制并修改配置文件：

```bash
cp src/main/resources/config.yml ./config.yml
```

编辑 `config.yml` 中的AI模型配置：

```yaml
ai:
  baseUrl: "http://your-internal-llm-host:8000/v1"
  model: "your-enterprise-model-name"
  apiKey: "sk-your-internal-api-key"
```

### 3. 准备知识库

在 `knowledge_base/` 目录下添加代码规范文档（Markdown格式）：

```
knowledge_base/
├── exception-handling.md
├── naming-conventions.md
├── performance-common-issues.md
└── ... (更多规范文件)
```

### 4. 创建索引

```bash
java -jar target/ai-code-review-assistant-1.0.0.jar index
```

### 5. 执行代码评审

```bash
java -jar target/ai-code-review-assistant-1.0.0.jar review \
  --repo-path /path/to/your/java-project \
  abc123def \
  HEAD
```

## 详细使用说明

### 索引命令

创建或更新知识库索引：

```bash
java -jar ai-code-review-assistant.jar index [OPTIONS]
```

选项：
- `-c, --config <path>`: 指定配置文件路径（默认：config.yml）
- `-f, --force`: 强制重建索引
- `-v, --verbose`: 启用详细输出

### 评审命令

执行AI代码评审：

```bash
java -jar ai-code-review-assistant.jar review [OPTIONS] <old-commit> <new-commit>
```

参数：
- `<old-commit>`: 旧commit哈希值
- `<new-commit>`: 新commit哈希值（可以是HEAD）

选项：
- `-c, --config <path>`: 指定配置文件路径
- `-r, --repo-path <path>`: Git仓库路径（默认：当前目录）
- `-v, --verbose`: 启用详细输出
- `--stats`: 显示配置统计信息

### 示例用法

```bash
# 创建索引
java -jar ai-code-review-assistant.jar index --verbose

# 评审当前分支相对于main分支的变更
java -jar ai-code-review-assistant.jar review main HEAD

# 评审特定commit范围的变更
java -jar ai-code-review-assistant.jar review \
  --repo-path /path/to/project \
  --verbose \
  a1b2c3d4 \
  e5f6g7h8

# 显示配置信息并执行评审
java -jar ai-code-review-assistant.jar review \
  --stats \
  feature-start \
  HEAD
```

## 配置说明

### AI配置

```yaml
ai:
  baseUrl: "http://your-llm-host:8000/v1"  # AI服务地址
  model: "your-model-name"                  # 模型名称
  apiKey: "sk-your-api-key"                # API密钥
  timeoutSeconds: 180                       # 请求超时时间
```

### RAG配置

```yaml
rag:
  knowledgeBasePath: "./knowledge_base"     # 知识库目录
  indexPath: "./.review_index"              # 索引存储目录
  topK: 5                                   # 检索返回的规范数量
```

### Prompt配置

```yaml
prompt:
  overallGoal: "你是一名资深的代码评审专家..."  # 评审目标
  outputFormat: |                              # 输出格式模板
    请严格按照以下Markdown格式输出评审报告：
    ...
```

## 知识库管理

### 添加新规范

1. 在 `knowledge_base/` 目录下创建新的Markdown文件
2. 使用清晰的标题和结构化内容
3. 重新运行索引命令更新搜索索引

### 规范文件格式示例

```markdown
# 规范：Java异常处理

## 资源管理
- **强制**: 必须使用 `try-with-resources` 或在 `finally` 块中关闭资源
- **示例**: 
  ```java
  try (FileInputStream fis = new FileInputStream("file.txt")) {
      // 处理文件
  }
  ```

## 异常捕获
- **禁止**: 生吞异常（空的catch块）
- **建议**: 记录完整的堆栈跟踪
```

## 故障排除

### 常见问题

1. **索引不存在错误**
   ```
   Error: Knowledge base index not found
   ```
   解决方案：运行 `java -jar ai-code-review-assistant.jar index`

2. **Git仓库未找到**
   ```
   Error: Not a git repository
   ```
   解决方案：确保在Git仓库目录中运行，或使用 `--repo-path` 指定正确路径

3. **AI API连接失败**
   ```
   Error: AI API request failed
   ```
   解决方案：检查配置文件中的AI服务地址和API密钥

### 调试模式

启用详细日志输出：

```bash
java -Ddebug=true -jar ai-code-review-assistant.jar review --verbose <old> <new>
```

## 架构说明

本工具采用模块化设计：

- `config`: 配置管理
- `git`: Git操作和差异提取
- `rag`: RAG核心（索引和检索）
- `ai`: AI服务集成
- `engine`: 评审流程协调
- `cli`: 命令行界面

## 许可证

本项目采用MIT许可证。
