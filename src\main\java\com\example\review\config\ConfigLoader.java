package com.example.review.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 配置文件加载器
 */
public class ConfigLoader {
    
    private static final Logger logger = LoggerFactory.getLogger(ConfigLoader.class);
    private static final String DEFAULT_CONFIG_PATH = "config.yml";
    
    private final ObjectMapper yamlMapper;
    
    public ConfigLoader() {
        this.yamlMapper = new ObjectMapper(new YAMLFactory());
    }
    
    /**
     * 加载配置文件
     * 优先从外部文件加载，如果不存在则从classpath加载默认配置
     * 
     * @return 配置对象
     * @throws IOException 配置文件读取失败
     */
    public ReviewConfig loadConfig() throws IOException {
        return loadConfig(DEFAULT_CONFIG_PATH);
    }
    
    /**
     * 加载指定路径的配置文件
     * 
     * @param configPath 配置文件路径
     * @return 配置对象
     * @throws IOException 配置文件读取失败
     */
    public ReviewConfig loadConfig(String configPath) throws IOException {
        ReviewConfig config;
        
        // 首先尝试从外部文件加载
        Path externalConfigPath = Paths.get(configPath);
        if (Files.exists(externalConfigPath)) {
            logger.info("Loading config from external file: {}", externalConfigPath.toAbsolutePath());
            config = yamlMapper.readValue(Files.newInputStream(externalConfigPath), ReviewConfig.class);
        } else {
            // 从classpath加载默认配置
            logger.info("Loading default config from classpath: {}", configPath);
            try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(configPath)) {
                if (inputStream == null) {
                    throw new IOException("Config file not found in classpath: " + configPath);
                }
                config = yamlMapper.readValue(inputStream, ReviewConfig.class);
            }
        }
        
        // 验证配置
        validateConfig(config);
        
        logger.info("Configuration loaded successfully");
        return config;
    }
    
    /**
     * 验证配置的完整性
     * 
     * @param config 配置对象
     * @throws IllegalArgumentException 配置验证失败
     */
    private void validateConfig(ReviewConfig config) {
        if (config == null) {
            throw new IllegalArgumentException("Config cannot be null");
        }
        
        // 验证AI配置
        ReviewConfig.AiConfig aiConfig = config.getAi();
        if (aiConfig == null) {
            throw new IllegalArgumentException("AI config is required");
        }
        if (aiConfig.getBaseUrl() == null || aiConfig.getBaseUrl().trim().isEmpty()) {
            throw new IllegalArgumentException("AI base URL is required");
        }
        if (aiConfig.getModel() == null || aiConfig.getModel().trim().isEmpty()) {
            throw new IllegalArgumentException("AI model is required");
        }
        if (aiConfig.getApiKey() == null || aiConfig.getApiKey().trim().isEmpty()) {
            throw new IllegalArgumentException("AI API key is required");
        }
        
        // 验证RAG配置
        ReviewConfig.RagConfig ragConfig = config.getRag();
        if (ragConfig == null) {
            throw new IllegalArgumentException("RAG config is required");
        }
        if (ragConfig.getKnowledgeBasePath() == null || ragConfig.getKnowledgeBasePath().trim().isEmpty()) {
            throw new IllegalArgumentException("Knowledge base path is required");
        }
        if (ragConfig.getIndexPath() == null || ragConfig.getIndexPath().trim().isEmpty()) {
            throw new IllegalArgumentException("Index path is required");
        }
        if (ragConfig.getTopK() <= 0) {
            throw new IllegalArgumentException("TopK must be positive");
        }
        
        // 验证Prompt配置
        ReviewConfig.PromptConfig promptConfig = config.getPrompt();
        if (promptConfig == null) {
            throw new IllegalArgumentException("Prompt config is required");
        }
        if (promptConfig.getOverallGoal() == null || promptConfig.getOverallGoal().trim().isEmpty()) {
            throw new IllegalArgumentException("Overall goal is required");
        }
        if (promptConfig.getOutputFormat() == null || promptConfig.getOutputFormat().trim().isEmpty()) {
            throw new IllegalArgumentException("Output format is required");
        }
    }
}
