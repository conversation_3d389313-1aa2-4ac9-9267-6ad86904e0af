package com.example.review.engine;

import com.example.review.ai.OpenAIService;
import com.example.review.config.ReviewConfig;
import com.example.review.git.GitService;
import com.example.review.rag.RetrievalService;
import org.apache.lucene.queryparser.classic.ParseException;
import org.eclipse.jgit.api.errors.GitAPIException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.List;

/**
 * 代码评审引擎，协调整个评审流程
 */
public class ReviewEngine implements AutoCloseable {
    
    private static final Logger logger = LoggerFactory.getLogger(ReviewEngine.class);
    
    private final ReviewConfig config;
    private final GitService gitService;
    private final RetrievalService retrievalService;
    private final OpenAIService openAIService;
    
    public ReviewEngine(ReviewConfig config) {
        this.config = config;
        this.gitService = new GitService();
        this.retrievalService = new RetrievalService(config.getRag());
        this.openAIService = new OpenAIService(config.getAi(), config.getPrompt());
    }
    
    /**
     * 执行完整的代码评审流程
     * 
     * @param repoPath Git仓库路径
     * @param oldCommit 旧commit哈希
     * @param newCommit 新commit哈希
     * @return 评审报告
     * @throws ReviewException 评审过程中发生错误
     */
    public String performReview(String repoPath, String oldCommit, String newCommit) throws ReviewException {
        logger.info("Starting code review process...");
        logger.info("Repository: {}", repoPath);
        logger.info("Comparing {} -> {}", oldCommit, newCommit);
        
        try {
            // 步骤1: 获取代码差异
            logger.info("Step 1: Extracting code differences...");
            String codeDiff = gitService.getDiff(repoPath, oldCommit, newCommit);
            
            if (codeDiff == null || codeDiff.trim().isEmpty()) {
                return "No code changes found between the specified commits.";
            }
            
            logger.info("Code diff extracted successfully (length: {} characters)", codeDiff.length());
            
            // 步骤2: 检索相关规范
            logger.info("Step 2: Retrieving relevant coding standards...");
            List<String> relevantRules;
            try {
                relevantRules = retrievalService.retrieveRelevantRules(codeDiff);
                logger.info("Retrieved {} relevant rules", relevantRules.size());
            } catch (ParseException e) {
                logger.warn("Failed to parse query for rule retrieval, using empty rules list", e);
                relevantRules = List.of();
            }
            
            // 步骤3: 调用AI进行评审
            logger.info("Step 3: Performing AI-powered code review...");
            String reviewResult = openAIService.performReview(codeDiff, relevantRules);
            
            logger.info("Code review completed successfully");
            return reviewResult;
            
        } catch (IOException e) {
            logger.error("IO error during code review", e);
            throw new ReviewException("IO error during code review: " + e.getMessage(), e);
        } catch (GitAPIException e) {
            logger.error("Git operation failed", e);
            throw new ReviewException("Git operation failed: " + e.getMessage(), e);
        } catch (Exception e) {
            logger.error("Unexpected error during code review", e);
            throw new ReviewException("Unexpected error during code review: " + e.getMessage(), e);
        }
    }
    
    /**
     * 验证评审前置条件
     * 
     * @param repoPath Git仓库路径
     * @param oldCommit 旧commit哈希
     * @param newCommit 新commit哈希
     * @throws ReviewException 验证失败
     */
    public void validateReviewParameters(String repoPath, String oldCommit, String newCommit) throws ReviewException {
        if (repoPath == null || repoPath.trim().isEmpty()) {
            throw new ReviewException("Repository path cannot be null or empty");
        }
        
        if (oldCommit == null || oldCommit.trim().isEmpty()) {
            throw new ReviewException("Old commit hash cannot be null or empty");
        }
        
        if (newCommit == null || newCommit.trim().isEmpty()) {
            throw new ReviewException("New commit hash cannot be null or empty");
        }
        
        if (oldCommit.equals(newCommit)) {
            throw new ReviewException("Old commit and new commit cannot be the same");
        }
        
        logger.debug("Review parameters validated successfully");
    }
    
    /**
     * 获取评审统计信息
     * 
     * @return 统计信息字符串
     */
    public String getReviewStats() {
        StringBuilder stats = new StringBuilder();
        stats.append("=== Code Review Configuration ===\n");
        stats.append("AI Model: ").append(config.getAi().getModel()).append("\n");
        stats.append("AI Base URL: ").append(config.getAi().getBaseUrl()).append("\n");
        stats.append("Knowledge Base Path: ").append(config.getRag().getKnowledgeBasePath()).append("\n");
        stats.append("Index Path: ").append(config.getRag().getIndexPath()).append("\n");
        stats.append("Top K Rules: ").append(config.getRag().getTopK()).append("\n");
        stats.append("Timeout: ").append(config.getAi().getTimeoutSeconds()).append(" seconds\n");
        
        return stats.toString();
    }
    
    /**
     * 关闭资源
     */
    @Override
    public void close() {
        try {
            if (retrievalService != null) {
                retrievalService.close();
            }
        } catch (IOException e) {
            logger.warn("Failed to close retrieval service", e);
        }
        
        try {
            if (openAIService != null) {
                openAIService.close();
            }
        } catch (Exception e) {
            logger.warn("Failed to close OpenAI service", e);
        }
    }
    
    /**
     * 代码评审异常类
     */
    public static class ReviewException extends Exception {
        public ReviewException(String message) {
            super(message);
        }
        
        public ReviewException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
