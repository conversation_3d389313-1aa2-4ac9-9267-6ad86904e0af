# 规范：常见性能问题

## 循环优化
- **禁止**: 在循环体内部进行数据库查询、RPC调用或创建昂贵的对象。应将这些操作移到循环外部。
- **示例**:
  ```java
  // 错误做法
  for (User user : users) {
      UserDetail detail = userService.getUserDetail(user.getId()); // 数据库查询
      processUser(user, detail);
  }
  
  // 正确做法
  List<Long> userIds = users.stream().map(User::getId).collect(Collectors.toList());
  Map<Long, UserDetail> detailMap = userService.batchGetUserDetails(userIds);
  for (User user : users) {
      UserDetail detail = detailMap.get(user.getId());
      processUser(user, detail);
  }
  ```

## 字符串处理
- **警惕**: 字符串拼接。在循环或频繁操作中，应使用 `StringBuilder` 代替 `+` 或 `String.concat`。
- **示例**:
  ```java
  // 错误做法
  String result = "";
  for (String item : items) {
      result += item + ","; // 每次都创建新的String对象
  }
  
  // 正确做法
  StringBuilder sb = new StringBuilder();
  for (String item : items) {
      sb.append(item).append(",");
  }
  String result = sb.toString();
  ```

## 数据结构选择
- **建议**: 选择合适的数据结构。例如，在需要快速查找的场景下，使用 `HashMap` 或 `HashSet` 而不是 `ArrayList`。
- **建议**: 对于大量数据的去重操作，使用 `Set` 而不是 `List.contains()`。
- **建议**: 需要保持插入顺序的Map使用 `LinkedHashMap`，需要排序的Map使用 `TreeMap`。

## 集合操作
- **建议**: 使用 `ArrayList` 时，如果能预估大小，在构造时指定初始容量避免扩容。
- **禁止**: 使用过时的集合类如 `Vector`、`Hashtable`，应使用对应的现代替代品。
- **建议**: 大集合的遍历优先使用迭代器或增强for循环，避免使用索引访问。

## 对象创建
- **建议**: 避免在循环中创建不必要的对象，特别是在高频调用的方法中。
- **建议**: 合理使用对象池技术处理昂贵对象的创建和销毁。
